import { useEffect } from 'react';
import { useNavigationState, type PageType } from './useNavigationState';
import { useCertificateType } from './useCertificateType';

/**
 * Hook to automatically mark a page as visited when the component mounts
 * This should be used in each form page component to track user navigation
 */
export const usePageVisit = (pageType: PageType) => {
  const { certificateType } = useCertificateType();
  const { markPageAsVisited } = useNavigationState(certificateType);

  useEffect(() => {
    // Only mark the page as visited if certificate type is available
    if (certificateType) {
      // markPageAsVisited is now async, but we don't need to await it here
      // as it's just updating the status in the background
      markPageAsVisited(pageType);
    }
  }, [pageType, markPageAsVisited, certificateType]);
};
