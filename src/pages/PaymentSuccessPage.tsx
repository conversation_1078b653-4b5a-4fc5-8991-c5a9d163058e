import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link, useSearch, useNavigate } from '@tanstack/react-router';
import { supabase } from '../lib/supabase';
import { useCertificate } from '../contexts/CertificateContext';
import { ErrorMessage, LoadingSpinner, SuccessMessage } from '../components/ui/StatusMessages';

interface PaymentSuccessSearchParams {
  certificate_id?: string;
}

export const PaymentSuccessPage = () => {
  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const search = useSearch({ from: '/payment-success' }) as PaymentSuccessSearchParams;
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { setActiveCertificateId } = useCertificate();

  const { certificate_id } = search;

  // Query to get certificate data
  const { data: certificateData, isLoading: isCertificateLoading } = useQuery({
    queryKey: ['energieausweise', certificate_id],
    queryFn: async () => {
      if (!certificate_id) return null;

      const { data, error } = await supabase
        .from('energieausweise')
        .select('*')
        .eq('id', certificate_id)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!certificate_id,
    retry: false,
  });

  // Mutation to update certificate status to paid
  const updatePaymentStatusMutation = useMutation({
    mutationFn: async () => {
      if (!certificate_id) {
        throw new Error('Keine Zertifikat-ID gefunden.');
      }

      // First, get the current certificate to check if order_number exists
      const { data: currentCert, error: fetchError } = await supabase
        .from('energieausweise')
        .select('order_number')
        .eq('id', certificate_id)
        .single();

      if (fetchError) {
        console.error('Error fetching certificate:', fetchError);
        throw fetchError;
      }

      // Generate order_number if it doesn't exist
      const orderNumber = currentCert?.order_number || `EA-${certificate_id.slice(-8).toUpperCase()}`;

      const { data, error } = await supabase
        .from('energieausweise')
        .update({
          status: 'paid',
          order_number: orderNumber,
          updated_at: new Date().toISOString(),
        })
        .eq('id', certificate_id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise'] });

      // Clean up any checkout-related localStorage items
      localStorage.removeItem('lastCheckoutSessionId');
      localStorage.removeItem('checkoutFormState');

      setIsVerifying(false);
    },
    onError: (error) => {
      console.error('Error updating payment status:', error);
      setVerificationError('Fehler beim Aktualisieren des Zahlungsstatus.');
      setIsVerifying(false);
    }
  });

  // Function for programmatic navigation
  const goToMyCertificates = () => {
    navigate({ to: '/meine-zertifikate' });
  };

  // Function to retry verification if it fails
  const retryVerification = () => {
    setIsVerifying(true);
    setVerificationError(null);
    updatePaymentStatusMutation.mutate();
  };

  useEffect(() => {
    // Verify payment and update status when component mounts
    if (certificate_id && certificateData) {
      // When payment is successful, update the active certificate
      setActiveCertificateId(certificate_id);

      // Check if payment is already marked as paid
      if (certificateData.status === 'paid') {
        setIsVerifying(false);
        return;
      }

      // Update payment status
      updatePaymentStatusMutation.mutate();
    } else if (certificate_id && !isCertificateLoading && !certificateData) {
      setVerificationError('Zertifikat nicht gefunden.');
      setIsVerifying(false);
    }
  }, [certificate_id, certificateData, isCertificateLoading, setActiveCertificateId]);

  if (!certificate_id) {
    return (
      <div className="max-w-2xl mx-auto px-4 py-8">
        <ErrorMessage message="Keine Zertifikat-ID in der URL gefunden. Bitte überprüfen Sie den Link." />
        <div className="mt-6">
          <button
            onClick={goToMyCertificates}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Zu meinen Zertifikaten
          </button>
        </div>
      </div>
    );
  }

  if (isVerifying || isCertificateLoading) {
    return (
      <div className="max-w-2xl mx-auto px-4 py-8">
        <LoadingSpinner message="Zahlung wird verifiziert..." />
        <p className="text-center text-gray-600 mt-4">
          Bitte warten Sie, während wir Ihre Zahlung bestätigen.
        </p>
      </div>
    );
  }

  if (verificationError) {
    return (
      <div className="max-w-2xl mx-auto px-4 py-8">
        <ErrorMessage
          message={verificationError}
          onRetry={retryVerification}
        />
        <div className="mt-6 space-x-4">
          <button
            onClick={goToMyCertificates}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Zu meinen Zertifikaten
          </button>
          <Link
            to="/erfassen/zusammenfassung"
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            Zurück zur Zusammenfassung
          </Link>
        </div>
      </div>
    );
  }

  // Success state
  return (
    <div className="max-w-2xl mx-auto px-4 py-8">
      <SuccessMessage message="Ihre Zahlung wurde erfolgreich verarbeitet. Ihr Energieausweis wird in Kürze erstellt." />

      <div className="bg-white shadow-md rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Vielen Dank für Ihren Kauf!</h1>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Bestelldetails</h3>
            <div className="mt-2 text-sm text-gray-600">
              <p><strong>Zertifikat-ID:</strong> {certificate_id}</p>
              <p><strong>Bestellnummer:</strong> {certificateData?.order_number || `EA-${certificate_id?.slice(-8).toUpperCase()}`}</p>
              <p><strong>Datum:</strong> {new Date().toLocaleDateString('de-DE')}</p>
              <p><strong>Betrag:</strong> €49,00</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900">Nächste Schritte</h3>
            <div className="mt-2 text-sm text-gray-600">
              <ul className="list-disc list-inside space-y-1">
                <li>Sie erhalten eine Bestätigungs-E-Mail mit allen Details</li>
                <li>Ihr Energieausweis wird innerhalb von 2-3 Werktagen erstellt</li>
                <li>Sie werden per E-Mail benachrichtigt, sobald der Ausweis verfügbar ist</li>
                <li>Der fertige Ausweis wird Ihnen als PDF zugesendet</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex space-x-4">
            <button
              onClick={goToMyCertificates}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
            >
              Zu meinen Zertifikaten
            </button>
            <Link
              to="/"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Zur Startseite
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
