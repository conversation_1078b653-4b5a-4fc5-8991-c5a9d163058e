-- Migration to implement page-based status tracking system
-- This replaces the generic status values with specific page names
-- to provide persistent navigation state tracking

-- First, let's examine the current status distribution
-- (This is for reference - the actual migration follows)

-- Update existing status values to page-based system
-- Map current generic statuses to appropriate page names based on certificate completion

UPDATE energieausweise 
SET status = CASE 
  -- For certificates that were marked as 'completed', set to 'zusammenfassung'
  -- since they reached the summary page
  WHEN status = 'completed' THEN 'zusammenfassung'
  
  -- For certificates that were 'paid', they definitely reached summary
  WHEN status = 'paid' THEN 'zusammenfassung'
  
  -- For certificates that are 'in_progress', we need to make an educated guess
  -- based on available data. Default to 'objektdaten' (first page)
  WHEN status = 'in_progress' THEN 'objektdaten'
  
  -- Handle any other edge cases by defaulting to first page
  ELSE 'objektdaten'
END;

-- Add a comment to document the new status field behavior
COMMENT ON COLUMN energieausweise.status IS 'Current page name in the certificate creation flow. Valid values depend on certificate_type: WG/V & NWG/V: objektdaten, gebaeudedetails1, gebaeudedetails2, verbrauch, zusammenfassung. WG/B: objektdaten, gebaeudedetails1, gebaeudedetails2, fenster, heizung, tww-lueftung, zusammenfassung.';

-- Create an index on status for better query performance
CREATE INDEX IF NOT EXISTS idx_energieausweise_status_certificate_type ON energieausweise(status, certificate_type);

-- Update the updated_at timestamp for all modified records
UPDATE energieausweise 
SET updated_at = NOW() 
WHERE status IN ('objektdaten', 'zusammenfassung');
